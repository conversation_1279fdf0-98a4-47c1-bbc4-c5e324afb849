#!/bin/bash

# Simple PGMQ Producer & Consumer Startup Script
# This script starts consumer and producer applications with proper startup sequence

set -e  # Exit on any error

# Color codes for better output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting PGMQ Producer & Consumer Applications${NC}"
echo "================================================================"

# Function to check if PM2 is available
if ! command -v pm2 &> /dev/null; then
    echo -e "${RED}❌ PM2 is not installed. Please install it first:${NC}"
    echo "npm install -g pm2"
    exit 1
fi

# Function to wait for process to be online
wait_for_process() {
    local process_name=$1
    local timeout=$2
    local count=0
    
    echo -e "${YELLOW}⏳ Waiting for $process_name to start...${NC}"
    
    while [ $count -lt $timeout ]; do
        if pm2 list | grep -q "$process_name.*online"; then
            echo -e "${GREEN}✅ $process_name is running${NC}"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    echo -e "${RED}❌ $process_name failed to start within $timeout seconds${NC}"
    return 1
}

# Step 1: Clean up any existing processes
echo -e "${BLUE}🧹 Cleaning up existing PM2 processes...${NC}"
pm2 delete all 2>/dev/null || true
echo -e "${GREEN}✅ Cleanup complete${NC}"

# Step 2: Prepare metrics directory
echo -e "${BLUE}📁 Preparing metrics directory...${NC}"
rm -rf metrics/
mkdir -p metrics
chmod 755 metrics
echo -e "${GREEN}✅ Metrics directory ready${NC}"

# Step 3: Install dependencies
echo -e "${BLUE}📦 Installing dependencies...${NC}"
npm install
echo -e "${GREEN}✅ Dependencies installed${NC}"

# Step 4: Start consumer first
echo -e "${BLUE}🔄 Starting consumer applications...${NC}"
pm2 start ecosystem.consumer.config.js

if ! wait_for_process "consumer" 15; then
    echo -e "${RED}❌ Failed to start consumer${NC}"
    pm2 logs consumer --lines 10
    exit 1
fi

# Give consumer time to initialize
echo -e "${YELLOW}⏳ Waiting 3 seconds for consumer initialization...${NC}"
sleep 3

# Step 5: Start producer
echo -e "${BLUE}📤 Starting producer applications...${NC}"
pm2 start ecosystem.producer.config.js

if ! wait_for_process "producer" 15; then
    echo -e "${RED}❌ Failed to start producer${NC}"
    pm2 logs producer --lines 10
    exit 1
fi

# Step 6: Show status
echo ""
echo -e "${GREEN}🎉 All applications started successfully!${NC}"
echo "================================================================"
pm2 list

echo ""
echo -e "${BLUE}📊 Useful commands:${NC}"
echo "  pm2 logs           - View all logs"
echo "  pm2 logs consumer  - View consumer logs"
echo "  pm2 logs producer  - View producer logs"
echo "  pm2 list           - Show process status"
echo "  pm2 delete all     - Stop all processes"
echo ""
echo -e "${BLUE}📈 Analysis commands:${NC}"
echo "  node validate-csv.js producer  - Validate producer metrics"
echo "  node validate-csv.js consumer  - Validate consumer metrics"
echo "  node compare-metrics.js        - Compare producer vs consumer"
echo ""

# Step 7: Monitor briefly
echo -e "${YELLOW}👀 Monitoring for 10 seconds...${NC}"
for i in {1..10}; do
    if ! pm2 list | grep -q "consumer.*online" || ! pm2 list | grep -q "producer.*online"; then
        echo -e "${RED}❌ One or more processes stopped unexpectedly${NC}"
        pm2 logs --lines 20
        exit 1
    fi
    echo -n "."
    sleep 1
done

echo ""
echo -e "${GREEN}✅ Applications are running stable. Metrics will be saved to ./metrics/${NC}"

# Show if any CSV files are being created
echo ""
if ls metrics/*.csv >/dev/null 2>&1; then
    echo -e "${GREEN}📄 CSV files created:${NC}"
    ls -la metrics/
else
    echo -e "${YELLOW}📄 No CSV files yet (normal for first few seconds)${NC}"
fi

echo ""
echo -e "${GREEN}🎯 Setup complete! Applications are running in the background.${NC}"
