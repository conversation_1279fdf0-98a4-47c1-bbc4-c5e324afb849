module.exports = {
  apps: [
    {
      name: 'producer',
      script: './producer.js',
      instances: '1', // Use all available CPU cores
      exec_mode: 'cluster',
      env: {
        DB_HOST: 'localhost',
        DB_PORT: '5433',
        DB_NAME: 'postgres',
        DB_USER: 'postgres',
        DB_PASSWORD: 'postgres',
        QUEUE_NAME: 'pgmq_test',
        TOTAL_MESSAGES: '10000',
        MESSAGE_SIZE_KB: '1'
      }
    }
  ]
};
