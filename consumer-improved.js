// Improved Consumer for PGMQ with better termination logic
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const workerId = process.env.pm_id || process.pid;
  console.log(`[${timestamp}] [Worker-${workerId}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Setup CSV file for consumer metrics with improved error handling
function setupCsvFile() {
  const metricsDir = path.join(__dirname, 'metrics');
  if (!fs.existsSync(metricsDir)) {
    fs.mkdirSync(metricsDir, { recursive: true, mode: 0o755 });
  }
  
  try {
    fs.chmodSync(metricsDir, 0o755);
  } catch (error) {
    // Ignore permission errors if we can't change them
  }
  
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];
  const fileName = `consumer_metrics_${timestamp}.csv`;
  const filePath = path.join(metricsDir, fileName);
  
  // Create CSV header only if file doesn't exist (shared across workers)
  if (!fs.existsSync(filePath)) {
    try {
      const lockDir = filePath + '.create_lock';
      let lockAcquired = false;
      
      for (let i = 0; i < 10; i++) {
        try {
          fs.mkdirSync(lockDir);
          lockAcquired = true;
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            require('child_process').execSync('sleep 0.01');
          } else {
            break;
          }
        }
      }
      
      try {
        if (!fs.existsSync(filePath)) {
          fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,received_timestamp,latency_ms,worker_id\n');
        }
      } finally {
        if (lockAcquired) {
          try {
            fs.rmdirSync(lockDir);
          } catch (err) {
            // Ignore cleanup errors
          }
        }
      }
    } catch (error) {
      if (!fs.existsSync(filePath)) {
        try {
          fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,received_timestamp,latency_ms,worker_id\n');
        } catch (err) {
          logWithTimestamp(`Failed to create CSV header: ${err.message}`);
        }
      }
    }
  }
  
  return filePath;
}

// Thread-safe CSV writing function
async function appendToCsv(filePath, data) {
  let retries = 3;
  while (retries > 0) {
    try {
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
      }
      
      const lockDir = filePath + '.lock_dir';
      let lockAcquired = false;
      
      for (let i = 0; i < 10; i++) {
        try {
          fs.mkdirSync(lockDir);
          lockAcquired = true;
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 20));
          } else {
            throw err;
          }
        }
      }
      
      if (!lockAcquired) {
        throw new Error('Could not acquire lock after multiple attempts');
      }
      
      try {
        fs.appendFileSync(filePath, data);
        break;
      } finally {
        try {
          fs.rmdirSync(lockDir);
        } catch (err) {
          // Ignore cleanup errors
        }
      }
    } catch (error) {
      retries--;
      if (retries === 0) {
        logWithTimestamp(`Failed to write to CSV after retries: ${error.message}`);
        try {
          fs.appendFileSync(filePath, data);
        } catch (fallbackError) {
          logWithTimestamp(`Fallback write also failed: ${fallbackError.message}`);
        }
      } else {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
      }
    }
  }
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runConsumer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const expectedTotalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  const workerId = process.env.pm_id || process.pid;
  const useQueueMetrics = process.env.USE_QUEUE_METRICS === 'true';
  
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);
  
  let processed = 0;
  let pgmq;
  let csvBuffer = [];
  const bufferSize = 50;
  let consecutiveEmptyReads = 0;
  const maxEmptyReads = 30; // Stop after 30 consecutive empty reads
  
  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    let startTime = Date.now();
    
    logWithTimestamp(`Starting consumer worker. Expected total messages: ${expectedTotalMessages}`);
    logWithTimestamp(`Using queue metrics for termination: ${useQueueMetrics}`);
    
    while (consecutiveEmptyReads < maxEmptyReads) {
      try {
        // Optionally check queue metrics to see if we should continue
        if (useQueueMetrics && consecutiveEmptyReads > 5) {
          try {
            const metrics = await pgmq.queue.getMetrics(queueName);
            if (metrics.queueLength === 0) {
              logWithTimestamp(`Queue is empty according to metrics. Stopping consumer.`);
              break;
            }
          } catch (metricsError) {
            logWithTimestamp(`Could not get queue metrics: ${metricsError.message}`);
          }
        }
        
        const msg = await pgmq.msg.read(queueName, 30);
        if (msg) {
          consecutiveEmptyReads = 0;
          const receiveTime = Date.now();
          const messageContent = msg.message;
          
          const messageSizeKB = messageContent.payload ? 
            Math.round(messageContent.payload.length / 1024) : 0;
          
          const latencyMs = receiveTime - messageContent.sendTime;
          
          csvBuffer.push(`${messageContent.id},${messageSizeKB},${messageContent.sendTime},${receiveTime},${latencyMs},${workerId}\n`);
          
          await pgmq.msg.archive(queueName, msg.msgId);
          processed++;
          
          if (csvBuffer.length >= bufferSize) {
            const dataToWrite = csvBuffer.join('');
            csvBuffer = [];
            await appendToCsv(csvFilePath, dataToWrite);
          }
          
          if (processed % 1000 === 0) {
            logWithTimestamp(`${processed} pesan telah diproses`);
          }
        } else {
          consecutiveEmptyReads++;
          if (consecutiveEmptyReads % 10 === 0) {
            logWithTimestamp(`No messages found for ${consecutiveEmptyReads} consecutive reads. Processed so far: ${processed}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        logWithTimestamp(`Error consumer: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    if (csvBuffer.length > 0) {
      const dataToWrite = csvBuffer.join('');
      await appendToCsv(csvFilePath, dataToWrite);
    }
    
    const endTime = Date.now();
    const throughput = processed / ((endTime - startTime) / 1000);
    logWithTimestamp(`Worker ${workerId} processed ${processed} messages`);
    logWithTimestamp(`Throughput Consumer: ${throughput.toFixed(2)} pesan/detik`);
    logWithTimestamp(`Consumer finished after ${consecutiveEmptyReads} consecutive empty reads`);
  } catch (error) {
    logWithTimestamp(`Error consumer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runConsumer();
