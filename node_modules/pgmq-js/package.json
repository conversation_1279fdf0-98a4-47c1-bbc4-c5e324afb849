{"name": "pgmq-js", "version": "1.3.0", "description": "Postgres Message Queue (PSMQ) JavaScript Client Library", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "repository": {"type": "github", "url": "https://github.com/<PERSON>-Magdi/pgmq-js"}, "scripts": {"build": "tsup", "start": "ts-node src/index.ts", "test": "jest --runInBand", "test:watch": "jest --watch --detect<PERSON><PERSON><PERSON>andles --runInBand", "test:cov": "jest --coverage --runInBand", "lint": "eslint . --fix", "lint:check": "eslint . --quiet", "format": "prettier . --write --ignore-unknown", "format:check": "prettier . --check --ignore-unknown"}, "keywords": ["postgres", "postgresql", "message-queue", "queues", "postgresql-extension", "pgmq"], "author": "muham<PERSON>-mag<PERSON>", "license": "Apache 2.0", "devDependencies": {"@faker-js/faker": "^9.0.0", "@types/jest": "^29.5.12", "@types/pg-pool": "^2.0.6", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "^8.56.0", "jest": "^29.7.0", "prettier": "^3.1.1", "ts-jest": "^29.2.5", "tsup": "^8.2.4", "typescript": "^5.6.2", "ts-node": "^10.9.2"}, "dependencies": {"pg": "^8.12.0", "pg-pool": "^3.6.2"}, "jest": {"globals": {"ts-jest": {"tsconfig": "tsconfig.json"}}, "moduleFileExtensions": ["ts", "js"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}