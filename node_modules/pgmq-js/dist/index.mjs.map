{"version": 3, "sources": ["../src/index.ts", "../src/constants.ts", "../src/queue-manager/helpers.ts", "../src/query-executer.ts", "../src/queue-manager/index.ts", "../src/msg-manager/helpers.ts", "../src/msg-manager/index.ts"], "sourcesContent": ["import { Client } from 'pg';\nimport Pool from 'pg-pool';\nimport { PgPoolConfig } from './types';\nimport {\n  DEFAULT_CONNECTION_TIMEOUT_MILLIS,\n  DEFAULT_IDLE_TIMEOUT_MILLIS,\n  DEFAULT_MAX_POOL_SIZE,\n  DEFAULT_MAX_USES,\n  DEFAULT_SSL,\n  defaultPgmqConfig,\n} from './constants';\nimport { QueueManager } from './queue-manager';\nimport { MsgManager } from './msg-manager';\n\nexport class Pgmq {\n  public readonly queue: QueueManager;\n  public readonly msg: MsgManager;\n\n  private constructor(private readonly pool: Pool<Client>) {\n    this.queue = new QueueManager(pool);\n    this.msg = new MsgManager(pool);\n  }\n\n  public static async new(c: PgPoolConfig, pgmqConfig = defaultPgmqConfig) {\n    if (c.max === undefined) c.max = DEFAULT_MAX_POOL_SIZE;\n    if (c.idleTimeoutMillis === undefined) c.max = DEFAULT_IDLE_TIMEOUT_MILLIS;\n    if (c.connectionTimeoutMillis === undefined)\n      c.connectionTimeoutMillis = DEFAULT_CONNECTION_TIMEOUT_MILLIS;\n    if (c.maxUses === undefined) c.maxUses = DEFAULT_MAX_USES;\n    if (c.ssl === undefined) c.ssl = DEFAULT_SSL;\n\n    const pool = new Pool(c);\n\n    const pgmq = new Pgmq(pool);\n    if (!pgmqConfig.skipExtensionCreation) await pgmq.prepare();\n\n    return pgmq;\n  }\n\n  private async prepare() {\n    const client = await this.pool.connect();\n    try {\n      await client.query('CREATE EXTENSION IF NOT EXISTS pgmq CASCADE;');\n    } finally {\n      client.release();\n    }\n  }\n\n  public async close() {\n    await this.pool.end();\n  }\n}\n", "import { PgmqConfig } from 'src/types';\n\nexport const DEFAULT_MAX_POOL_SIZE = 20;\nexport const DEFAULT_IDLE_TIMEOUT_MILLIS = 1000;\nexport const DEFAULT_CONNECTION_TIMEOUT_MILLIS = 1000;\nexport const DEFAULT_MAX_USES = 7500;\nexport const DEFAULT_SSL = true;\n\nexport const defaultPgmqConfig: PgmqConfig = {\n  skipExtensionCreation: false,\n};\n", "import { DbQueueMetrics, Queue, QueueMetrics } from './types';\n\nexport const parseDbQueue = (q: string): Queue => {\n  const parts = q.substring(1, q.length - 1).split(',');\n  return {\n    name: parts[0],\n    isPartitioned: parts[1] === 't',\n    isUnlogged: parts[2] === 't',\n    createdAt: new Date(parts[3]),\n  };\n};\n\nexport const parseDbQueueMetrics = (m: DbQueueMetrics): QueueMetrics => ({\n  queueName: m.queue_name,\n  queueLength: parseInt(m.queue_length),\n  newestMsgAgeSec: m.newest_msg_age_sec != null ? parseInt(m.newest_msg_age_sec) : undefined,\n  oldestMsgAgeSec: m.oldest_msg_age_sec != null ? parseInt(m.oldest_msg_age_sec) : undefined,\n  totalMessages: parseInt(m.total_messages),\n  scrapeTime: new Date(m.scrape_time),\n});\n", "import { Client, QueryResultRow } from 'pg';\nimport Pool from 'pg-pool';\n\nexport class QueryExecuter {\n  constructor(protected readonly pool: Pool<Client>) {}\n\n  protected async executeQuery<T extends QueryResultRow>(query: string, params?: unknown[]) {\n    const client = await this.pool.connect();\n\n    try {\n      return await client.query<T>(query, params);\n    } finally {\n      client.release();\n    }\n  }\n}\n", "import { parseDbQueue, parseDbQueueMetrics } from './helpers';\nimport { DbQueueMetrics, Queue } from './types';\nimport { QueryExecuter } from '../query-executer';\n\nexport class QueueManager extends QueryExecuter {\n  public async list(): Promise<Queue[]> {\n    const query = 'SELECT pgmq.list_queues()';\n    const { rows } = await this.executeQuery<{ list_queues: string }>(query);\n    return rows.map(({ list_queues }) => parseDbQueue(list_queues));\n  }\n\n  public async create(name: string) {\n    const query = 'SELECT pgmq.create($1)';\n    await this.executeQuery(query, [name]);\n  }\n\n  public async createUnlogged(name: string) {\n    const query = 'SELECT pgmq.create_unlogged($1)';\n    await this.executeQuery(query, [name]);\n  }\n\n  public async drop(name: string) {\n    const query = 'SELECT pgmq.drop_queue($1)';\n    await this.executeQuery(query, [name]);\n  }\n\n  public async purge(name: string) {\n    const query = 'SELECT pgmq.purge_queue($1)';\n    await this.executeQuery(query, [name]);\n  }\n\n  public async detachArchive(name: string) {\n    const query = 'SELECT pgmq.detach_archive($1)';\n    await this.executeQuery(query, [name]);\n  }\n\n  public async getMetrics(name: string) {\n    const query = 'SELECT * FROM pgmq.metrics($1);';\n    const { rows } = await this.executeQuery<DbQueueMetrics>(query, [name]);\n    return parseDbQueueMetrics(rows[0]);\n  }\n\n  public async getAllMetrics() {\n    const query = 'SELECT * FROM pgmq.metrics_all()';\n    const { rows } = await this.executeQuery<DbQueueMetrics>(query);\n    return rows.map(parseDbQueueMetrics);\n  }\n}\n", "import { DbMessage, Message } from './types';\n\nexport const parseDbMessage = <T>(m?: DbMessage): Message<T> | undefined => {\n  if (m == null) return m;\n  return {\n    msgId: parseInt(m.msg_id),\n    readCount: parseInt(m.read_ct),\n    enqueuedAt: new Date(m.enqueued_at),\n    vt: new Date(m.vt),\n    message: m.message as T,\n  };\n};\n", "import { parseDbMessage } from './helpers';\nimport { DbMessage, Message } from 'src/msg-manager/types';\nimport { QueryExecuter } from 'src/query-executer';\n\nexport class MsgManager extends QueryExecuter {\n  public async send<T>(q: string, msg: T, delay = 0): Promise<number> {\n    const query = 'SELECT * FROM pgmq.send($1, $2, $3)';\n    const res = await this.executeQuery<{ send: number }>(query, [q, JSON.stringify(msg), delay]);\n    return res.rows[0].send;\n  }\n\n  public async sendBatch<T>(q: string, msgs: T[], delay = 0): Promise<number[]> {\n    const query = 'SELECT * FROM pgmq.send_batch($1, $2::jsonb[], $3)';\n    const res = await this.executeQuery<{ send_batch: number }>(query, [\n      q,\n      msgs.map((m) => JSON.stringify(m)),\n      delay,\n    ]);\n\n    return res.rows.flatMap((s) => s.send_batch);\n  }\n\n  public async read<T>(q: string, vt = 0): Promise<Message<T>> {\n    return this.readBatch<T>(q, vt, 1).then((msgs) => msgs[0]);\n  }\n\n  public async readBatch<T>(q: string, vt: number, numMessages: number): Promise<Message<T>[]> {\n    const query = 'SELECT * FROM pgmq.read($1, $2, $3)';\n    const res = await this.executeQuery<DbMessage>(query, [q, vt, numMessages]);\n    return res.rows.flatMap(parseDbMessage<T>) as Message<T>[];\n  }\n\n  public async pop<T>(q: string): Promise<Message<T> | undefined> {\n    const query = 'SELECT * FROM pgmq.pop($1)';\n    const res = await this.executeQuery<DbMessage>(query, [q]);\n    return parseDbMessage(res.rows[0]);\n  }\n\n  public async archive(q: string, msgId: number): Promise<boolean> {\n    const query = 'SELECT pgmq.archive($1, $2::bigint)';\n    const res = await this.executeQuery<{ archive: boolean }>(query, [q, msgId]);\n    return res.rows[0].archive;\n  }\n\n  public async archiveBatch(q: string, msgIds: number[]): Promise<number[]> {\n    const query = 'SELECT pgmq.archive($1, $2::bigint[])';\n    const res = await this.executeQuery<{ archive: number }>(query, [q, msgIds]);\n    return res.rows.flatMap((a) => a.archive);\n  }\n\n  public async delete(q: string, msgId: number): Promise<boolean> {\n    const query = 'SELECT pgmq.delete($1, $2::bigint)';\n    const res = await this.executeQuery<{ delete: boolean }>(query, [q, msgId]);\n    return res.rows[0].delete;\n  }\n\n  public async deleteBatch(q: string, msgIds: number[]): Promise<number[]> {\n    const query = 'SELECT pgmq.delete($1, $2::bigint[])';\n    const res = await this.executeQuery<{ delete: number }>(query, [q, msgIds]);\n    return res.rows.flatMap((d) => d.delete);\n  }\n\n  public async setVt<T>(\n    q: string,\n    msgId: number,\n    vtOffset: number,\n  ): Promise<Message<T> | undefined> {\n    const query = 'SELECT * FROM pgmq.set_vt($1, $2, $3);';\n    const res = await this.executeQuery<DbMessage>(query, [q, msgId, vtOffset]);\n    return parseDbMessage<T>(res.rows[0]);\n  }\n}\n"], "mappings": "6MACA,OAAOA,MAAU,UCCV,IAAMC,EAAwB,GACxBC,EAA8B,IAC9BC,EAAoC,IACpCC,EAAmB,KACnBC,EAAc,GAEdC,EAAgC,CAC3C,sBAAuB,EACzB,ECRO,IAAMC,EAAgBC,GAAqB,CAChD,IAAMC,EAAQD,EAAE,UAAU,EAAGA,EAAE,OAAS,CAAC,EAAE,MAAM,GAAG,EACpD,MAAO,CACL,KAAMC,EAAM,CAAC,EACb,cAAeA,EAAM,CAAC,IAAM,IAC5B,WAAYA,EAAM,CAAC,IAAM,IACzB,UAAW,IAAI,KAAKA,EAAM,CAAC,CAAC,CAC9B,CACF,EAEaC,EAAuBC,IAAqC,CACvE,UAAWA,EAAE,WACb,YAAa,SAASA,EAAE,YAAY,EACpC,gBAAiBA,EAAE,oBAAsB,KAAO,SAASA,EAAE,kBAAkB,EAAI,OACjF,gBAAiBA,EAAE,oBAAsB,KAAO,SAASA,EAAE,kBAAkB,EAAI,OACjF,cAAe,SAASA,EAAE,cAAc,EACxC,WAAY,IAAI,KAAKA,EAAE,WAAW,CACpC,GChBO,IAAMC,EAAN,KAAoB,CACzB,YAA+BC,EAAoB,CAApB,UAAAA,CAAqB,CAEpC,aAAuCC,EAAeC,EAAoB,QAAAC,EAAA,sBACxF,IAAMC,EAAS,MAAM,KAAK,KAAK,QAAQ,EAEvC,GAAI,CACF,OAAO,MAAMA,EAAO,MAASH,EAAOC,CAAM,CAC5C,QAAE,CACAE,EAAO,QAAQ,CACjB,CACF,GACF,ECXO,IAAMC,EAAN,cAA2BC,CAAc,CACjC,MAAyB,QAAAC,EAAA,sBACpC,IAAMC,EAAQ,4BACR,CAAE,KAAAC,CAAK,EAAI,MAAM,KAAK,aAAsCD,CAAK,EACvE,OAAOC,EAAK,IAAI,CAAC,CAAE,YAAAC,CAAY,IAAMC,EAAaD,CAAW,CAAC,CAChE,GAEa,OAAOE,EAAc,QAAAL,EAAA,sBAEhC,MAAM,KAAK,aADG,yBACiB,CAACK,CAAI,CAAC,CACvC,GAEa,eAAeA,EAAc,QAAAL,EAAA,sBAExC,MAAM,KAAK,aADG,kCACiB,CAACK,CAAI,CAAC,CACvC,GAEa,KAAKA,EAAc,QAAAL,EAAA,sBAE9B,MAAM,KAAK,aADG,6BACiB,CAACK,CAAI,CAAC,CACvC,GAEa,MAAMA,EAAc,QAAAL,EAAA,sBAE/B,MAAM,KAAK,aADG,8BACiB,CAACK,CAAI,CAAC,CACvC,GAEa,cAAcA,EAAc,QAAAL,EAAA,sBAEvC,MAAM,KAAK,aADG,iCACiB,CAACK,CAAI,CAAC,CACvC,GAEa,WAAWA,EAAc,QAAAL,EAAA,sBACpC,IAAMC,EAAQ,kCACR,CAAE,KAAAC,CAAK,EAAI,MAAM,KAAK,aAA6BD,EAAO,CAACI,CAAI,CAAC,EACtE,OAAOC,EAAoBJ,EAAK,CAAC,CAAC,CACpC,GAEa,eAAgB,QAAAF,EAAA,sBAC3B,IAAMC,EAAQ,mCACR,CAAE,KAAAC,CAAK,EAAI,MAAM,KAAK,aAA6BD,CAAK,EAC9D,OAAOC,EAAK,IAAII,CAAmB,CACrC,GACF,EC7CO,IAAMC,EAAqBC,GAC5BA,GAAK,KAAaA,EACf,CACL,MAAO,SAASA,EAAE,MAAM,EACxB,UAAW,SAASA,EAAE,OAAO,EAC7B,WAAY,IAAI,KAAKA,EAAE,WAAW,EAClC,GAAI,IAAI,KAAKA,EAAE,EAAE,EACjB,QAASA,EAAE,OACb,ECNK,IAAMC,EAAN,cAAyBC,CAAc,CAC/B,KAAQC,EAAWC,EAAQC,EAAQ,EAAoB,QAAAC,EAAA,sBAGlE,OADY,MAAM,KAAK,aADT,sCAC+C,CAACH,EAAG,KAAK,UAAUC,CAAG,EAAGC,CAAK,CAAC,GACjF,KAAK,CAAC,EAAE,IACrB,GAEa,UAAaF,EAAWI,EAAWF,EAAQ,EAAsB,QAAAC,EAAA,sBAQ5E,OANY,MAAM,KAAK,aADT,qDACqD,CACjEH,EACAI,EAAK,IAAKC,GAAM,KAAK,UAAUA,CAAC,CAAC,EACjCH,CACF,CAAC,GAEU,KAAK,QAASI,GAAMA,EAAE,UAAU,CAC7C,GAEa,KAAQN,EAAWO,EAAK,EAAwB,QAAAJ,EAAA,sBAC3D,OAAO,KAAK,UAAaH,EAAGO,EAAI,CAAC,EAAE,KAAMH,GAASA,EAAK,CAAC,CAAC,CAC3D,GAEa,UAAaJ,EAAWO,EAAYC,EAA4C,QAAAL,EAAA,sBAG3F,OADY,MAAM,KAAK,aADT,sCACwC,CAACH,EAAGO,EAAIC,CAAW,CAAC,GAC/D,KAAK,QAAQC,CAAiB,CAC3C,GAEa,IAAOT,EAA4C,QAAAG,EAAA,sBAE9D,IAAMO,EAAM,MAAM,KAAK,aADT,6BACwC,CAACV,CAAC,CAAC,EACzD,OAAOS,EAAeC,EAAI,KAAK,CAAC,CAAC,CACnC,GAEa,QAAQV,EAAWW,EAAiC,QAAAR,EAAA,sBAG/D,OADY,MAAM,KAAK,aADT,sCACmD,CAACH,EAAGW,CAAK,CAAC,GAChE,KAAK,CAAC,EAAE,OACrB,GAEa,aAAaX,EAAWY,EAAqC,QAAAT,EAAA,sBAGxE,OADY,MAAM,KAAK,aADT,wCACkD,CAACH,EAAGY,CAAM,CAAC,GAChE,KAAK,QAASC,GAAMA,EAAE,OAAO,CAC1C,GAEa,OAAOb,EAAWW,EAAiC,QAAAR,EAAA,sBAG9D,OADY,MAAM,KAAK,aADT,qCACkD,CAACH,EAAGW,CAAK,CAAC,GAC/D,KAAK,CAAC,EAAE,MACrB,GAEa,YAAYX,EAAWY,EAAqC,QAAAT,EAAA,sBAGvE,OADY,MAAM,KAAK,aADT,uCACiD,CAACH,EAAGY,CAAM,CAAC,GAC/D,KAAK,QAASE,GAAMA,EAAE,MAAM,CACzC,GAEa,MACXd,EACAW,EACAI,EACiC,QAAAZ,EAAA,sBAEjC,IAAMO,EAAM,MAAM,KAAK,aADT,yCACwC,CAACV,EAAGW,EAAOI,CAAQ,CAAC,EAC1E,OAAON,EAAkBC,EAAI,KAAK,CAAC,CAAC,CACtC,GACF,ENzDO,IAAMM,EAAN,MAAMC,CAAK,CAIR,YAA6BC,EAAoB,CAApB,UAAAA,EACnC,KAAK,MAAQ,IAAIC,EAAaD,CAAI,EAClC,KAAK,IAAM,IAAIE,EAAWF,CAAI,CAChC,CAEA,OAAoB,IAAIG,EAAiD,QAAAC,EAAA,yBAAjDC,EAAiBC,EAAaC,EAAmB,CACnEF,EAAE,MAAQ,SAAWA,EAAE,IAAMG,GAC7BH,EAAE,oBAAsB,SAAWA,EAAE,IAAMI,GAC3CJ,EAAE,0BAA4B,SAChCA,EAAE,wBAA0BK,GAC1BL,EAAE,UAAY,SAAWA,EAAE,QAAUM,GACrCN,EAAE,MAAQ,SAAWA,EAAE,IAAMO,GAEjC,IAAMZ,EAAO,IAAIa,EAAKR,CAAC,EAEjBS,EAAO,IAAIf,EAAKC,CAAI,EAC1B,OAAKM,EAAW,wBAAuB,MAAMQ,EAAK,QAAQ,GAEnDA,CACT,GAEc,SAAU,QAAAV,EAAA,sBACtB,IAAMW,EAAS,MAAM,KAAK,KAAK,QAAQ,EACvC,GAAI,CACF,MAAMA,EAAO,MAAM,8CAA8C,CACnE,QAAE,CACAA,EAAO,QAAQ,CACjB,CACF,GAEa,OAAQ,QAAAX,EAAA,sBACnB,MAAM,KAAK,KAAK,IAAI,CACtB,GACF", "names": ["Pool", "DEFAULT_MAX_POOL_SIZE", "DEFAULT_IDLE_TIMEOUT_MILLIS", "DEFAULT_CONNECTION_TIMEOUT_MILLIS", "DEFAULT_MAX_USES", "DEFAULT_SSL", "defaultPgmqConfig", "parseDbQueue", "q", "parts", "parseDbQueueMetrics", "m", "QueryExecuter", "pool", "query", "params", "__async", "client", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "QueryExecuter", "__async", "query", "rows", "list_queues", "parseDbQueue", "name", "parseDbQueueMetrics", "parseDbMessage", "m", "MsgManager", "QueryExecuter", "q", "msg", "delay", "__async", "msgs", "m", "s", "vt", "numMessages", "parseDbMessage", "res", "msgId", "msgIds", "a", "d", "vtOffset", "Pgmq", "_Pgmq", "pool", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MsgManager", "_0", "__async", "c", "pgmqConfig", "defaultPgmqConfig", "DEFAULT_MAX_POOL_SIZE", "DEFAULT_IDLE_TIMEOUT_MILLIS", "DEFAULT_CONNECTION_TIMEOUT_MILLIS", "DEFAULT_MAX_USES", "DEFAULT_SSL", "Pool", "pgmq", "client"]}