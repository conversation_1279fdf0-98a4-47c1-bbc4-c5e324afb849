"use strict";var S=Object.create;var p=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var Q=Object.getOwnPropertyNames;var D=Object.getPrototypeOf,I=Object.prototype.hasOwnProperty;var $=(s,e)=>{for(var t in e)p(s,t,{get:e[t],enumerable:!0})},T=(s,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Q(e))!I.call(s,i)&&i!==t&&p(s,i,{get:()=>e[i],enumerable:!(r=C(e,i))||r.enumerable});return s};var P=(s,e,t)=>(t=s!=null?S(D(s)):{},T(e||!s||!s.__esModule?p(t,"default",{value:s,enumerable:!0}):t,s)),A=s=>T(p({},"__esModule",{value:!0}),s);var n=(s,e,t)=>new Promise((r,i)=>{var u=a=>{try{y(t.next(a))}catch(d){i(d)}},c=a=>{try{y(t.throw(a))}catch(d){i(d)}},y=a=>a.done?r(a.value):Promise.resolve(a.value).then(u,c);y((t=t.apply(s,e)).next())});var O={};$(O,{Pgmq:()=>q});module.exports=A(O);var L=P(require("pg-pool"));var E=20,M=1e3,_=1e3,f=7500,w=!0,h={skipExtensionCreation:!1};var x=s=>{let e=s.substring(1,s.length-1).split(",");return{name:e[0],isPartitioned:e[1]==="t",isUnlogged:e[2]==="t",createdAt:new Date(e[3])}},b=s=>({queueName:s.queue_name,queueLength:parseInt(s.queue_length),newestMsgAgeSec:s.newest_msg_age_sec!=null?parseInt(s.newest_msg_age_sec):void 0,oldestMsgAgeSec:s.oldest_msg_age_sec!=null?parseInt(s.oldest_msg_age_sec):void 0,totalMessages:parseInt(s.total_messages),scrapeTime:new Date(s.scrape_time)});var o=class{constructor(e){this.pool=e}executeQuery(e,t){return n(this,null,function*(){let r=yield this.pool.connect();try{return yield r.query(e,t)}finally{r.release()}})}};var g=class extends o{list(){return n(this,null,function*(){let e="SELECT pgmq.list_queues()",{rows:t}=yield this.executeQuery(e);return t.map(({list_queues:r})=>x(r))})}create(e){return n(this,null,function*(){yield this.executeQuery("SELECT pgmq.create($1)",[e])})}createUnlogged(e){return n(this,null,function*(){yield this.executeQuery("SELECT pgmq.create_unlogged($1)",[e])})}drop(e){return n(this,null,function*(){yield this.executeQuery("SELECT pgmq.drop_queue($1)",[e])})}purge(e){return n(this,null,function*(){yield this.executeQuery("SELECT pgmq.purge_queue($1)",[e])})}detachArchive(e){return n(this,null,function*(){yield this.executeQuery("SELECT pgmq.detach_archive($1)",[e])})}getMetrics(e){return n(this,null,function*(){let t="SELECT * FROM pgmq.metrics($1);",{rows:r}=yield this.executeQuery(t,[e]);return b(r[0])})}getAllMetrics(){return n(this,null,function*(){let e="SELECT * FROM pgmq.metrics_all()",{rows:t}=yield this.executeQuery(e);return t.map(b)})}};var l=s=>s==null?s:{msgId:parseInt(s.msg_id),readCount:parseInt(s.read_ct),enqueuedAt:new Date(s.enqueued_at),vt:new Date(s.vt),message:s.message};var m=class extends o{send(e,t,r=0){return n(this,null,function*(){return(yield this.executeQuery("SELECT * FROM pgmq.send($1, $2, $3)",[e,JSON.stringify(t),r])).rows[0].send})}sendBatch(e,t,r=0){return n(this,null,function*(){return(yield this.executeQuery("SELECT * FROM pgmq.send_batch($1, $2::jsonb[], $3)",[e,t.map(c=>JSON.stringify(c)),r])).rows.flatMap(c=>c.send_batch)})}read(e,t=0){return n(this,null,function*(){return this.readBatch(e,t,1).then(r=>r[0])})}readBatch(e,t,r){return n(this,null,function*(){return(yield this.executeQuery("SELECT * FROM pgmq.read($1, $2, $3)",[e,t,r])).rows.flatMap(l)})}pop(e){return n(this,null,function*(){let r=yield this.executeQuery("SELECT * FROM pgmq.pop($1)",[e]);return l(r.rows[0])})}archive(e,t){return n(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.archive($1, $2::bigint)",[e,t])).rows[0].archive})}archiveBatch(e,t){return n(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.archive($1, $2::bigint[])",[e,t])).rows.flatMap(u=>u.archive)})}delete(e,t){return n(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.delete($1, $2::bigint)",[e,t])).rows[0].delete})}deleteBatch(e,t){return n(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.delete($1, $2::bigint[])",[e,t])).rows.flatMap(u=>u.delete)})}setVt(e,t,r){return n(this,null,function*(){let u=yield this.executeQuery("SELECT * FROM pgmq.set_vt($1, $2, $3);",[e,t,r]);return l(u.rows[0])})}};var q=class s{constructor(e){this.pool=e;this.queue=new g(e),this.msg=new m(e)}static new(r){return n(this,arguments,function*(e,t=h){e.max===void 0&&(e.max=E),e.idleTimeoutMillis===void 0&&(e.max=M),e.connectionTimeoutMillis===void 0&&(e.connectionTimeoutMillis=_),e.maxUses===void 0&&(e.maxUses=f),e.ssl===void 0&&(e.ssl=w);let i=new L.default(e),u=new s(i);return t.skipExtensionCreation||(yield u.prepare()),u})}prepare(){return n(this,null,function*(){let e=yield this.pool.connect();try{yield e.query("CREATE EXTENSION IF NOT EXISTS pgmq CASCADE;")}finally{e.release()}})}close(){return n(this,null,function*(){yield this.pool.end()})}};0&&(module.exports={Pgmq});
//# sourceMappingURL=index.js.map