import * as pg from 'pg';
import { Client, QueryResultRow } from 'pg';
import Pool from 'pg-pool';

interface PgPoolConfig {
    host: string;
    database: string;
    user: string;
    password: string;
    port: number;
    ssl?: boolean;
    max?: number;
    idleTimeoutMillis?: number;
    connectionTimeoutMillis?: number;
    maxUses?: number;
}
interface PgmqConfig {
    skipExtensionCreation?: boolean;
}

interface Queue {
    name: string;
    createdAt: Date;
    isPartitioned: boolean;
    isUnlogged: boolean;
}
interface QueueMetrics {
    queueName: string;
    queueLength: number;
    newestMsgAgeSec?: number;
    oldestMsgAgeSec?: number;
    totalMessages: number;
    scrapeTime: Date;
}

declare class QueryExecuter {
    protected readonly pool: Pool<Client>;
    constructor(pool: Pool<Client>);
    protected executeQuery<T extends QueryResultRow>(query: string, params?: unknown[]): Promise<pg.QueryResult<T>>;
}

declare class QueueManager extends QueryExecuter {
    list(): Promise<Queue[]>;
    create(name: string): Promise<void>;
    createUnlogged(name: string): Promise<void>;
    drop(name: string): Promise<void>;
    purge(name: string): Promise<void>;
    detachArchive(name: string): Promise<void>;
    getMetrics(name: string): Promise<QueueMetrics>;
    getAllMetrics(): Promise<QueueMetrics[]>;
}

interface Message<T> {
    msgId: number;
    readCount: number;
    enqueuedAt: Date;
    vt: Date;
    message: T;
}

declare class MsgManager extends QueryExecuter {
    send<T>(q: string, msg: T, delay?: number): Promise<number>;
    sendBatch<T>(q: string, msgs: T[], delay?: number): Promise<number[]>;
    read<T>(q: string, vt?: number): Promise<Message<T>>;
    readBatch<T>(q: string, vt: number, numMessages: number): Promise<Message<T>[]>;
    pop<T>(q: string): Promise<Message<T> | undefined>;
    archive(q: string, msgId: number): Promise<boolean>;
    archiveBatch(q: string, msgIds: number[]): Promise<number[]>;
    delete(q: string, msgId: number): Promise<boolean>;
    deleteBatch(q: string, msgIds: number[]): Promise<number[]>;
    setVt<T>(q: string, msgId: number, vtOffset: number): Promise<Message<T> | undefined>;
}

declare class Pgmq {
    private readonly pool;
    readonly queue: QueueManager;
    readonly msg: MsgManager;
    private constructor();
    static new(c: PgPoolConfig, pgmqConfig?: PgmqConfig): Promise<Pgmq>;
    private prepare;
    close(): Promise<void>;
}

export { Pgmq };
