var r=(s,e,t)=>new Promise((n,u)=>{var i=a=>{try{m(t.next(a))}catch(y){u(y)}},c=a=>{try{m(t.throw(a))}catch(y){u(y)}},m=a=>a.done?n(a.value):Promise.resolve(a.value).then(i,c);m((t=t.apply(s,e)).next())});import h from"pg-pool";var b=20,q=1e3,T=1e3,E=7500,M=!0,_={skipExtensionCreation:!1};var f=s=>{let e=s.substring(1,s.length-1).split(",");return{name:e[0],isPartitioned:e[1]==="t",isUnlogged:e[2]==="t",createdAt:new Date(e[3])}},d=s=>({queueName:s.queue_name,queueLength:parseInt(s.queue_length),newestMsgAgeSec:s.newest_msg_age_sec!=null?parseInt(s.newest_msg_age_sec):void 0,oldestMsgAgeSec:s.oldest_msg_age_sec!=null?parseInt(s.oldest_msg_age_sec):void 0,totalMessages:parseInt(s.total_messages),scrapeTime:new Date(s.scrape_time)});var o=class{constructor(e){this.pool=e}executeQuery(e,t){return r(this,null,function*(){let n=yield this.pool.connect();try{return yield n.query(e,t)}finally{n.release()}})}};var p=class extends o{list(){return r(this,null,function*(){let e="SELECT pgmq.list_queues()",{rows:t}=yield this.executeQuery(e);return t.map(({list_queues:n})=>f(n))})}create(e){return r(this,null,function*(){yield this.executeQuery("SELECT pgmq.create($1)",[e])})}createUnlogged(e){return r(this,null,function*(){yield this.executeQuery("SELECT pgmq.create_unlogged($1)",[e])})}drop(e){return r(this,null,function*(){yield this.executeQuery("SELECT pgmq.drop_queue($1)",[e])})}purge(e){return r(this,null,function*(){yield this.executeQuery("SELECT pgmq.purge_queue($1)",[e])})}detachArchive(e){return r(this,null,function*(){yield this.executeQuery("SELECT pgmq.detach_archive($1)",[e])})}getMetrics(e){return r(this,null,function*(){let t="SELECT * FROM pgmq.metrics($1);",{rows:n}=yield this.executeQuery(t,[e]);return d(n[0])})}getAllMetrics(){return r(this,null,function*(){let e="SELECT * FROM pgmq.metrics_all()",{rows:t}=yield this.executeQuery(e);return t.map(d)})}};var g=s=>s==null?s:{msgId:parseInt(s.msg_id),readCount:parseInt(s.read_ct),enqueuedAt:new Date(s.enqueued_at),vt:new Date(s.vt),message:s.message};var l=class extends o{send(e,t,n=0){return r(this,null,function*(){return(yield this.executeQuery("SELECT * FROM pgmq.send($1, $2, $3)",[e,JSON.stringify(t),n])).rows[0].send})}sendBatch(e,t,n=0){return r(this,null,function*(){return(yield this.executeQuery("SELECT * FROM pgmq.send_batch($1, $2::jsonb[], $3)",[e,t.map(c=>JSON.stringify(c)),n])).rows.flatMap(c=>c.send_batch)})}read(e,t=0){return r(this,null,function*(){return this.readBatch(e,t,1).then(n=>n[0])})}readBatch(e,t,n){return r(this,null,function*(){return(yield this.executeQuery("SELECT * FROM pgmq.read($1, $2, $3)",[e,t,n])).rows.flatMap(g)})}pop(e){return r(this,null,function*(){let n=yield this.executeQuery("SELECT * FROM pgmq.pop($1)",[e]);return g(n.rows[0])})}archive(e,t){return r(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.archive($1, $2::bigint)",[e,t])).rows[0].archive})}archiveBatch(e,t){return r(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.archive($1, $2::bigint[])",[e,t])).rows.flatMap(i=>i.archive)})}delete(e,t){return r(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.delete($1, $2::bigint)",[e,t])).rows[0].delete})}deleteBatch(e,t){return r(this,null,function*(){return(yield this.executeQuery("SELECT pgmq.delete($1, $2::bigint[])",[e,t])).rows.flatMap(i=>i.delete)})}setVt(e,t,n){return r(this,null,function*(){let i=yield this.executeQuery("SELECT * FROM pgmq.set_vt($1, $2, $3);",[e,t,n]);return g(i.rows[0])})}};var w=class s{constructor(e){this.pool=e;this.queue=new p(e),this.msg=new l(e)}static new(n){return r(this,arguments,function*(e,t=_){e.max===void 0&&(e.max=b),e.idleTimeoutMillis===void 0&&(e.max=q),e.connectionTimeoutMillis===void 0&&(e.connectionTimeoutMillis=T),e.maxUses===void 0&&(e.maxUses=E),e.ssl===void 0&&(e.ssl=M);let u=new h(e),i=new s(u);return t.skipExtensionCreation||(yield i.prepare()),i})}prepare(){return r(this,null,function*(){let e=yield this.pool.connect();try{yield e.query("CREATE EXTENSION IF NOT EXISTS pgmq CASCADE;")}finally{e.release()}})}close(){return r(this,null,function*(){yield this.pool.end()})}};export{w as Pgmq};
//# sourceMappingURL=index.mjs.map