{"name": "proper-lockfile", "version": "4.1.2", "description": "A inter-process and inter-machine lockfile utility that works on a local or network file system", "keywords": ["lock", "locking", "file", "lockfile", "fs", "cross-process"], "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/moxystudio/node-proper-lockfile", "repository": {"type": "git", "url": "**************:moxystudio/node-proper-lockfile.git"}, "license": "MIT", "main": "index.js", "files": ["lib"], "scripts": {"lint": "eslint .", "test": "jest --env node --coverage --runInBand", "prerelease": "npm t && npm run lint", "release": "standard-version", "postrelease": "git push --follow-tags origin HEAD && npm publish"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"graceful-fs": "^4.2.4", "retry": "^0.12.0", "signal-exit": "^3.0.2"}, "devDependencies": {"@commitlint/cli": "^7.0.0", "@commitlint/config-conventional": "^7.0.1", "@segment/clear-timeouts": "^2.0.0", "delay": "^4.1.0", "eslint": "^5.3.0", "eslint-config-moxy": "^7.1.0", "execa": "^1.0.0", "husky": "^1.1.4", "jest": "^24.5.0", "lint-staged": "^8.0.4", "mkdirp": "^0.5.1", "p-defer": "^2.1.0", "rimraf": "^2.6.2", "stable": "^0.1.8", "standard-version": "^5.0.0", "thread-sleep": "^2.1.0"}}