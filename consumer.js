// Consumer for PGMQ, reads and processes messages from the queue for benchmarking
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const lockfile = require('proper-lockfile');
const cluster = require('cluster');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const workerId = process.env.pm_id || process.pid;
  console.log(`[${timestamp}] [Worker-${workerId}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Setup CSV file for consumer metrics with improved error handling
function setupCsvFile() {
  // Ensure metrics directory exists with proper permissions
  const metricsDir = path.join(__dirname, 'metrics');
  if (!fs.existsSync(metricsDir)) {
    fs.mkdirSync(metricsDir, { recursive: true, mode: 0o755 });
  }

  // Ensure directory has proper permissions (handles existing directory case)
  try {
    fs.chmodSync(metricsDir, 0o755);
  } catch (error) {
    // Ignore permission errors if we can't change them
  }

  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];
  const fileName = `consumer_metrics_${timestamp}.csv`;
  const filePath = path.join(metricsDir, fileName);

  // Create CSV header only if file doesn't exist (shared across workers)
  if (!fs.existsSync(filePath)) {
    try {
      // Simple lock mechanism using directory creation
      const lockDir = filePath + '.create_lock';
      let lockAcquired = false;

      for (let i = 0; i < 10; i++) {
        try {
          fs.mkdirSync(lockDir);
          lockAcquired = true;
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            // Wait a bit and try again
            require('child_process').execSync('sleep 0.01');
          } else {
            break;
          }
        }
      }

      try {
        // Double-check after acquiring lock
        if (!fs.existsSync(filePath)) {
          fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,received_timestamp,latency_ms,worker_id\n');
        }
      } finally {
        if (lockAcquired) {
          try {
            fs.rmdirSync(lockDir);
          } catch (err) {
            // Ignore cleanup errors
          }
        }
      }
    } catch (error) {
      // If locking fails, try to create file anyway (fallback)
      if (!fs.existsSync(filePath)) {
        try {
          fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,received_timestamp,latency_ms,worker_id\n');
        } catch (err) {
          logWithTimestamp(`Failed to create CSV header: ${err.message}`);
        }
      }
    }
  }

  return filePath;
}

// Thread-safe CSV writing function with improved error handling
async function appendToCsv(filePath, data) {
  let retries = 3;
  while (retries > 0) {
    try {
      // Ensure the directory exists before attempting any operations
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
      }

      // Create a simple lock directory instead of using proper-lockfile
      const lockDir = filePath + '.lock_dir';
      let lockAcquired = false;

      // Try to create lock directory (atomic operation)
      for (let i = 0; i < 10; i++) {
        try {
          fs.mkdirSync(lockDir);
          lockAcquired = true;
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            // Lock exists, wait a bit
            await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 20));
          } else {
            throw err;
          }
        }
      }

      if (!lockAcquired) {
        throw new Error('Could not acquire lock after multiple attempts');
      }

      try {
        fs.appendFileSync(filePath, data);
        break;
      } finally {
        // Release lock by removing directory
        try {
          fs.rmdirSync(lockDir);
        } catch (err) {
          // Ignore cleanup errors
        }
      }
    } catch (error) {
      retries--;
      if (retries === 0) {
        logWithTimestamp(`Failed to write to CSV after retries: ${error.message}`);
        // Fallback: write without locking (may cause minor corruption)
        try {
          fs.appendFileSync(filePath, data);
        } catch (fallbackError) {
          logWithTimestamp(`Fallback write also failed: ${fallbackError.message}`);
        }
      } else {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
      }
    }
  }
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runConsumer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const expectedTotalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  const workerId = process.env.pm_id || process.pid;

  // Setup CSV file (shared across all workers)
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);

  let processed = 0;
  let pgmq;
  let csvBuffer = [];
  const bufferSize = 50; // Reduced buffer size for more frequent writes
  let consecutiveEmptyReads = 0;
  const maxEmptyReads = 30; // Stop after 30 consecutive empty reads (30 seconds)

  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    let startTime = Date.now();

    logWithTimestamp(`Starting consumer worker. Expected total messages: ${expectedTotalMessages}`);

    // Process messages until queue is empty (not until a fixed count)
    while (consecutiveEmptyReads < maxEmptyReads) {
      try {
        const msg = await pgmq.msg.read(queueName, 30);
        if (msg) {
          consecutiveEmptyReads = 0; // Reset counter when we find a message
          const receiveTime = Date.now();
          const messageContent = msg.message;

          // Calculate message size from payload
          const messageSizeKB = messageContent.payload ?
            Math.round(messageContent.payload.length / 1024) : 0;

          // Calculate latency
          const latencyMs = receiveTime - messageContent.sendTime;

          // Add to buffer with worker ID
          csvBuffer.push(`${messageContent.id},${messageSizeKB},${messageContent.sendTime},${receiveTime},${latencyMs},${workerId}\n`);

          await pgmq.msg.archive(queueName, msg.msgId);
          processed++;

          // Write buffer to file periodically with thread-safe operations
          if (csvBuffer.length >= bufferSize) {
            const dataToWrite = csvBuffer.join('');
            csvBuffer = [];
            await appendToCsv(csvFilePath, dataToWrite);
          }

          if (processed % 1000 === 0) {
            logWithTimestamp(`${processed} pesan telah diproses`);
          }
        } else {
          consecutiveEmptyReads++;
          if (consecutiveEmptyReads % 10 === 0) {
            logWithTimestamp(`No messages found for ${consecutiveEmptyReads} consecutive reads. Processed so far: ${processed}`);
          }
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait longer when queue is empty
        }
      } catch (error) {
        logWithTimestamp(`Error consumer: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Write any remaining data in buffer
    if (csvBuffer.length > 0) {
      const dataToWrite = csvBuffer.join('');
      await appendToCsv(csvFilePath, dataToWrite);
    }

    const endTime = Date.now();
    const throughput = processed / ((endTime - startTime) / 1000);
    logWithTimestamp(`Worker ${workerId} processed ${processed} messages`);
    logWithTimestamp(`Throughput Consumer: ${throughput.toFixed(2)} pesan/detik`);
    logWithTimestamp(`Consumer finished after ${consecutiveEmptyReads} consecutive empty reads`);
  } catch (error) {
    logWithTimestamp(`Error consumer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runConsumer();
