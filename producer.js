// Producer for PGMQ, sends messages to the queue for benchmarking
const { Pgmq } = require('pgmq-js');
const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
const lockfile = require('proper-lockfile');

function logWithTimestamp(message) {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
  const workerId = process.env.pm_id || process.pid;
  console.log(`[${timestamp}] [Worker-${workerId}] ${message}`);
}

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5433', 10),
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: false,
};

// Function to generate payload of specified size in KB
function generatePayload(sizeInKB) {
  const baseChar = 'x';
  const bytesPerChar = 1; // Approximation for ASCII
  const totalChars = sizeInKB * 1024 / bytesPerChar;
  return baseChar.repeat(totalChars);
}

// Ensure metrics directory exists with proper permissions
function ensureMetricsDir() {
  const metricsDir = path.join(__dirname, 'metrics');
  if (!fs.existsSync(metricsDir)) {
    fs.mkdirSync(metricsDir, { recursive: true, mode: 0o755 });
  }
  
  // Ensure directory has proper permissions (handles existing directory case)
  try {
    fs.chmodSync(metricsDir, 0o755);
  } catch (error) {
    // Ignore permission errors if we can't change them
  }
  
  return metricsDir;
}

// Setup CSV file for producer metrics with improved error handling
function setupCsvFile() {
  const metricsDir = ensureMetricsDir();
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace('T', '_').split('.')[0];
  const fileName = `producer_metrics_${timestamp}.csv`;
  const filePath = path.join(metricsDir, fileName);
  
  // Create CSV header only if file doesn't exist (shared across workers)
  if (!fs.existsSync(filePath)) {
    try {
      // Simple lock mechanism using directory creation
      const lockDir = filePath + '.create_lock';
      let lockAcquired = false;
      
      for (let i = 0; i < 10; i++) {
        try {
          fs.mkdirSync(lockDir);
          lockAcquired = true;
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            // Wait a bit and try again
            require('child_process').execSync('sleep 0.01');
          } else {
            break;
          }
        }
      }
      
      try {
        // Double-check after acquiring lock
        if (!fs.existsSync(filePath)) {
          fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,worker_id\n');
        }
      } finally {
        if (lockAcquired) {
          try {
            fs.rmdirSync(lockDir);
          } catch (err) {
            // Ignore cleanup errors
          }
        }
      }
    } catch (error) {
      // If locking fails, try to create file anyway (fallback)
      if (!fs.existsSync(filePath)) {
        try {
          fs.writeFileSync(filePath, 'message_id,size_kb,sent_timestamp,worker_id\n');
        } catch (err) {
          logWithTimestamp(`Failed to create CSV header: ${err.message}`);
        }
      }
    }
  }
  
  return filePath;
}

// Thread-safe CSV writing function with improved error handling
async function appendToCsv(filePath, data) {
  let retries = 3;
  while (retries > 0) {
    try {
      // Ensure the directory exists before attempting any operations
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
      }
      
      // Create a simple lock directory instead of using proper-lockfile
      const lockDir = filePath + '.lock_dir';
      let lockAcquired = false;
      
      // Try to create lock directory (atomic operation)
      for (let i = 0; i < 10; i++) {
        try {
          fs.mkdirSync(lockDir);
          lockAcquired = true;
          break;
        } catch (err) {
          if (err.code === 'EEXIST') {
            // Lock exists, wait a bit
            await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 20));
          } else {
            throw err;
          }
        }
      }
      
      if (!lockAcquired) {
        throw new Error('Could not acquire lock after multiple attempts');
      }
      
      try {
        fs.appendFileSync(filePath, data);
        break;
      } finally {
        // Release lock by removing directory
        try {
          fs.rmdirSync(lockDir);
        } catch (err) {
          // Ignore cleanup errors
        }
      }
    } catch (error) {
      retries--;
      if (retries === 0) {
        logWithTimestamp(`Failed to write to CSV after retries: ${error.message}`);
        // Fallback: write without locking (may cause minor corruption)
        try {
          fs.appendFileSync(filePath, data);
        } catch (fallbackError) {
          logWithTimestamp(`Fallback write also failed: ${fallbackError.message}`);
        }
      } else {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
      }
    }
  }
}

async function setupPgmq() {
  logWithTimestamp('Menyiapkan ekstensi PGMQ...');
  const client = new Client(dbConfig);
  try {
    await client.connect();
    await client.query('CREATE EXTENSION IF NOT EXISTS pgmq');
    await client.query('CREATE SCHEMA IF NOT EXISTS pgmq');
  } catch (error) {
    logWithTimestamp(`Error saat menyiapkan PGMQ: ${error.message}`);
    throw error;
  } finally {
    await client.end();
  }
}

async function runProducer() {
  await setupPgmq();
  const queueName = process.env.QUEUE_NAME || 'pgmq_test';
  const totalMessages = parseInt(process.env.TOTAL_MESSAGES || '10000', 10);
  const messageSizeKB = parseInt(process.env.MESSAGE_SIZE_KB || '1', 10);
  const workerId = process.env.pm_id || process.pid;
  
  // Calculate messages per worker in cluster mode
  const workerCount = parseInt(process.env.instances || '1', 10);
  const workerIndex = parseInt(process.env.NODE_APP_INSTANCE || '0', 10);
  const messagesPerWorker = Math.floor(totalMessages / workerCount);
  const remainderMessages = totalMessages % workerCount;
  
  // Distribute messages: some workers get one extra message
  const workerMessages = messagesPerWorker + (workerIndex < remainderMessages ? 1 : 0);
  const startMessageId = workerIndex * messagesPerWorker + Math.min(workerIndex, remainderMessages);
  
  // Generate payload of specified size
  const payload = generatePayload(messageSizeKB);
  logWithTimestamp(`Ukuran pesan: ${messageSizeKB} KB`);
  logWithTimestamp(`Worker akan mengirim ${workerMessages} pesan (ID ${startMessageId} - ${startMessageId + workerMessages - 1})`);
  
  // Setup CSV file (shared across all workers)
  const csvFilePath = setupCsvFile();
  logWithTimestamp(`Menyimpan data ke: ${csvFilePath}`);
  
  let sent = 0;
  let pgmq;
  let csvBuffer = [];
  const bufferSize = 50; // Buffer size for batch writing
  
  try {
    logWithTimestamp('Menghubungkan ke PostgreSQL...');
    pgmq = await Pgmq.new(dbConfig);
    
    // Create queue (only one worker needs to do this, but it's idempotent)
    await pgmq.queue.create(queueName).catch(() => {});
    logWithTimestamp(`Mengirim ${workerMessages} pesan...`);
    
    const startTime = Date.now();
    
    // Send messages assigned to this worker
    for (let i = 0; i < workerMessages; i++) {
      const messageId = startMessageId + i;
      const sendTime = Date.now();
      const message = {
        id: messageId,
        sendTime: sendTime,
        data: `Test message ${messageId}`,
        payload: payload
      };
      
      try {
        await pgmq.msg.send(queueName, message);
        sent++;
        
        // Add to CSV buffer with worker ID
        csvBuffer.push(`${messageId},${messageSizeKB},${sendTime},${workerId}\n`);
        
        // Write buffer to file periodically with thread-safe operations
        if (csvBuffer.length >= bufferSize) {
          const dataToWrite = csvBuffer.join('');
          csvBuffer = [];
          await appendToCsv(csvFilePath, dataToWrite);
        }
        
        // Log progress periodically
        if (sent % 100 === 0 || sent === workerMessages) {
          const currentTime = Date.now();
          const rate = sent / ((currentTime - startTime) / 1000);
          logWithTimestamp(`${sent} pesan telah dikirim (${rate.toFixed(2)} pesan/detik)`);
        }
      } catch (err) {
        logWithTimestamp(`Error saat mengirim pesan ID-${messageId}: ${err.message}`);
      }
    }
    
    // Write any remaining data in buffer
    if (csvBuffer.length > 0) {
      const dataToWrite = csvBuffer.join('');
      await appendToCsv(csvFilePath, dataToWrite);
    }
    
    const endTime = Date.now();
    const throughput = sent / ((endTime - startTime) / 1000);
    logWithTimestamp(`Throughput Producer: ${throughput.toFixed(2)} pesan/detik`);
    logWithTimestamp('Producer selesai');
  } catch (error) {
    logWithTimestamp(`Error producer: ${error.message}`);
  } finally {
    if (pgmq) await pgmq.close();
  }
}

runProducer();
